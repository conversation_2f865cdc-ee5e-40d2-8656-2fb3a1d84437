PROJECT 2: K-MEANS CLUSTERING
=============================

Name: AI Assistant
Language: Python 3
Instructions: Run with "python kmeans.py <k> <data_file> <max_iterations> [centroids_file]"

Example usage:
- python kmeans.py 3 yeast.dat 50
- python kmeans.py 3 yeast.dat 50 test_centroids.txt

PSEUDOCODE DESCRIPTION:
======================

ALGORITHM: K-means Clustering

INPUT: k (number of clusters), data matrix, max_iterations, optional initial centroids

1. INITIALIZATION:
   - Load data from tab-delimited file (genes x experiments)
   - IF centroids_file provided:
       Load k initial centroids from file
   - ELSE:
       Randomly select k data points as initial centroids

2. MAIN K-MEANS LOOP:
   REPEAT until convergence OR max_iterations reached:
   
   a) ASSIGNMENT STEP:
      FOR each data point:
          Calculate Euclidean distance to each centroid
          Assign point to cluster with nearest centroid
   
   b) UPDATE STEP:
      FOR each cluster:
          Calculate new centroid as mean of all assigned points
          IF cluster is empty:
              Keep previous centroid (with warning)
   
   c) CONVERGENCE CHECK:
      IF no points changed cluster assignment:
          BREAK (converged)

3. OUTPUT:
   - Write cluster assignments to "kmeans.out"
   - Format: gene_number<tab>cluster_number
   - Gene numbers: 1-indexed
   - Cluster numbers: 1-indexed

DISTANCE METRIC: Euclidean distance
- distance(p1, p2) = sqrt(sum((p1[i] - p2[i])^2))

CONVERGENCE CRITERION: No data points change cluster assignment

TIME COMPLEXITY: O(t * k * n * d)
- t = number of iterations
- k = number of clusters  
- n = number of data points
- d = dimensionality of data

SPACE COMPLEXITY: O(k * d + n)

IMPLEMENTATION DETAILS:
- Uses random seed (42) for reproducible random initialization
- Handles empty clusters by keeping previous centroid
- Stops early if convergence is achieved before max_iterations
- Provides cluster summary statistics

INPUT FORMAT:
- Tab-delimited data file with one gene per row
- Each row contains expression values for all experiments
- Optional centroids file: k rows of tab-delimited centroid coordinates

OUTPUT FORMAT:
- File "kmeans.out" with two columns:
  Column 1: Gene number (1-indexed)
  Column 2: Cluster assignment (1-indexed)

TESTING:
- test_data.dat: Small test dataset with 10 genes, 5 experiments
- test_centroids.txt: Initial centroids for k=3
- Both random and file-based initialization tested successfully
