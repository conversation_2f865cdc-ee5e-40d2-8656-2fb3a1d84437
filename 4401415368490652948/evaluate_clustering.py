#!/usr/bin/env python3
"""
聚类结果评估工具
计算聚类质量的各种指标
"""

import math
import sys

def load_data(filename):
    """加载基因表达数据"""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            if line.strip():
                values = [float(x) for x in line.strip().split('\t')]
                data.append(values)
    return data

def load_clusters(filename):
    """加载聚类结果"""
    clusters = []
    with open(filename, 'r') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split('\t')
                gene_id = int(parts[0]) - 1  # 转换为0索引
                cluster_id = int(parts[1]) - 1  # 转换为0索引
                clusters.append(cluster_id)
    return clusters

def euclidean_distance(point1, point2):
    """计算欧几里得距离"""
    distance = 0.0
    for i in range(len(point1)):
        distance += (point1[i] - point2[i]) ** 2
    return math.sqrt(distance)

def calculate_centroid(points):
    """计算质心"""
    if not points:
        return None
    
    dimensions = len(points[0])
    centroid = [0.0] * dimensions
    
    for point in points:
        for i in range(dimensions):
            centroid[i] += point[i]
    
    for i in range(dimensions):
        centroid[i] /= len(points)
    
    return centroid

def calculate_within_cluster_sum_of_squares(data, clusters, k):
    """计算簇内平方和 (WCSS)"""
    # 按聚类分组数据
    cluster_groups = [[] for _ in range(k)]
    for i, cluster_id in enumerate(clusters):
        cluster_groups[cluster_id].append(data[i])
    
    total_wcss = 0.0
    cluster_wcss = []
    
    for cluster_idx, cluster_points in enumerate(cluster_groups):
        if not cluster_points:
            cluster_wcss.append(0.0)
            continue
        
        # 计算质心
        centroid = calculate_centroid(cluster_points)
        
        # 计算该聚类的WCSS
        wcss = 0.0
        for point in cluster_points:
            distance = euclidean_distance(point, centroid)
            wcss += distance ** 2
        
        cluster_wcss.append(wcss)
        total_wcss += wcss
    
    return total_wcss, cluster_wcss

def calculate_between_cluster_sum_of_squares(data, clusters, k):
    """计算簇间平方和 (BCSS)"""
    # 计算总体质心
    overall_centroid = calculate_centroid(data)
    
    # 按聚类分组
    cluster_groups = [[] for _ in range(k)]
    for i, cluster_id in enumerate(clusters):
        cluster_groups[cluster_id].append(data[i])
    
    bcss = 0.0
    for cluster_points in cluster_groups:
        if not cluster_points:
            continue
        
        cluster_centroid = calculate_centroid(cluster_points)
        distance = euclidean_distance(cluster_centroid, overall_centroid)
        bcss += len(cluster_points) * (distance ** 2)
    
    return bcss

def calculate_silhouette_sample(data, clusters, point_idx):
    """计算单个点的轮廓系数"""
    point = data[point_idx]
    point_cluster = clusters[point_idx]
    
    # 计算a(i): 同簇内其他点的平均距离
    same_cluster_distances = []
    for i, cluster_id in enumerate(clusters):
        if i != point_idx and cluster_id == point_cluster:
            distance = euclidean_distance(point, data[i])
            same_cluster_distances.append(distance)
    
    if len(same_cluster_distances) == 0:
        a_i = 0  # 如果簇中只有一个点
    else:
        a_i = sum(same_cluster_distances) / len(same_cluster_distances)
    
    # 计算b(i): 到最近其他簇的平均距离
    k = max(clusters) + 1
    other_cluster_avg_distances = []
    
    for cluster_id in range(k):
        if cluster_id == point_cluster:
            continue
        
        distances_to_cluster = []
        for i, c_id in enumerate(clusters):
            if c_id == cluster_id:
                distance = euclidean_distance(point, data[i])
                distances_to_cluster.append(distance)
        
        if distances_to_cluster:
            avg_distance = sum(distances_to_cluster) / len(distances_to_cluster)
            other_cluster_avg_distances.append(avg_distance)
    
    if not other_cluster_avg_distances:
        return 0  # 只有一个簇的情况
    
    b_i = min(other_cluster_avg_distances)
    
    # 计算轮廓系数
    if max(a_i, b_i) == 0:
        return 0
    else:
        return (b_i - a_i) / max(a_i, b_i)

def evaluate_clustering(data_file, cluster_file):
    """评估聚类结果"""
    print("正在加载数据...")
    data = load_data(data_file)
    clusters = load_clusters(cluster_file)
    
    k = max(clusters) + 1
    n_points = len(data)
    
    print(f"数据概况:")
    print(f"- 基因数量: {n_points}")
    print(f"- 特征维度: {len(data[0])}")
    print(f"- 聚类数量: {k}")
    
    # 聚类分布
    cluster_counts = [0] * k
    for cluster_id in clusters:
        cluster_counts[cluster_id] += 1
    
    print(f"\n聚类分布:")
    for i, count in enumerate(cluster_counts):
        percentage = (count / n_points) * 100
        print(f"- 聚类 {i+1}: {count} 个基因 ({percentage:.1f}%)")
    
    # 计算WCSS和BCSS
    print(f"\n正在计算聚类质量指标...")
    total_wcss, cluster_wcss = calculate_within_cluster_sum_of_squares(data, clusters, k)
    bcss = calculate_between_cluster_sum_of_squares(data, clusters, k)
    
    print(f"\n聚类质量指标:")
    print(f"- 簇内平方和 (WCSS): {total_wcss:.2f}")
    print(f"- 簇间平方和 (BCSS): {bcss:.2f}")
    print(f"- 总平方和 (TSS): {total_wcss + bcss:.2f}")
    print(f"- 簇间/总体比例: {bcss/(total_wcss + bcss):.3f}")
    
    print(f"\n各聚类的簇内平方和:")
    for i, wcss in enumerate(cluster_wcss):
        if cluster_counts[i] > 0:
            avg_wcss = wcss / cluster_counts[i]
            print(f"- 聚类 {i+1}: {wcss:.2f} (平均: {avg_wcss:.2f})")
    
    # 计算轮廓系数 (对于大数据集，只计算样本)
    print(f"\n正在计算轮廓系数...")
    sample_size = min(500, n_points)  # 最多计算500个样本
    import random
    random.seed(42)
    sample_indices = random.sample(range(n_points), sample_size)
    
    silhouette_scores = []
    for idx in sample_indices:
        score = calculate_silhouette_sample(data, clusters, idx)
        silhouette_scores.append(score)
    
    avg_silhouette = sum(silhouette_scores) / len(silhouette_scores)
    print(f"- 平均轮廓系数 (基于{sample_size}个样本): {avg_silhouette:.3f}")
    
    # 轮廓系数解释
    if avg_silhouette > 0.7:
        interpretation = "优秀"
    elif avg_silhouette > 0.5:
        interpretation = "良好"
    elif avg_silhouette > 0.25:
        interpretation = "一般"
    else:
        interpretation = "较差"
    
    print(f"- 聚类质量评价: {interpretation}")
    
    return {
        'wcss': total_wcss,
        'bcss': bcss,
        'silhouette': avg_silhouette,
        'cluster_counts': cluster_counts
    }

def main():
    if len(sys.argv) != 3:
        print("使用方法: python evaluate_clustering.py <数据文件> <聚类结果文件>")
        print("示例: python evaluate_clustering.py data_proj_2/yeast.dat.txt kmeans.out")
        sys.exit(1)
    
    data_file = sys.argv[1]
    cluster_file = sys.argv[2]
    
    try:
        results = evaluate_clustering(data_file, cluster_file)
        
        print(f"\n=== 聚类评估总结 ===")
        print(f"轮廓系数越接近1越好 (当前: {results['silhouette']:.3f})")
        print(f"簇内平方和越小越好 (当前: {results['wcss']:.2f})")
        print(f"簇间平方和越大越好 (当前: {results['bcss']:.2f})")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
