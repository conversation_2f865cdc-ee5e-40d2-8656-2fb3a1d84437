#!/usr/bin/env python3

def debug_traceback():
    # Recreate the exact DP matrix from our algorithm
    seqA = "AATGC"
    seqB = "AGGC"
    gap_penalty = 0
    
    score_matrix = {
        ('A', 'A'): 1, ('A', 'T'): 0, ('A', 'G'): 0, ('A', 'C'): 0,
        ('T', 'A'): 0, ('T', 'T'): 1, ('T', 'G'): 0, ('T', 'C'): 0,
        ('G', 'A'): 0, ('G', 'T'): 0, ('G', 'G'): 1, ('G', 'C'): 0,
        ('C', 'A'): 0, ('C', 'T'): 0, ('C', 'G'): 0, ('C', 'C'): 1,
    }
    
    # Build DP matrix
    rows, cols = len(seqA) + 1, len(seqB) + 1
    M = [[0.0 for _ in range(cols)] for _ in range(rows)]
    
    # Fill first row and column
    for i in range(rows):
        M[i][0] = gap_penalty * i
    for j in range(cols):
        M[0][j] = gap_penalty * j
    
    # Fill matrix
    for i in range(1, rows):
        for j in range(1, cols):
            match_score = score_matrix.get((seqA[i-1], seqB[j-1]), 0)
            match = M[i-1][j-1] + match_score
            delete = M[i-1][j] + gap_penalty
            insert = M[i][j-1] + gap_penalty
            M[i][j] = max(match, delete, insert)
    
    # Print matrix
    print("DP Matrix:")
    print("    ", end="")
    for c in " " + seqB:
        print(f"{c:6}", end="")
    print()
    for i in range(rows):
        if i == 0:
            print(f"  ", end="")
        else:
            print(f"{seqA[i-1]} ", end="")
        for j in range(cols):
            print(f"{M[i][j]:6.1f}", end="")
        print()
    
    # Detailed traceback
    aligned_a, aligned_b = "", ""
    i, j = len(seqA), len(seqB)
    
    print(f"\nStarting traceback from ({i}, {j})")
    
    while i > 0 and j > 0:
        score = M[i][j]
        diagonal = M[i-1][j-1]
        up = M[i][j-1]
        left = M[i-1][j]
        
        match_score = score_matrix.get((seqA[i-1], seqB[j-1]), 0)
        
        print(f"At ({i},{j}): score={score}")
        print(f"  diagonal={diagonal}, up={up}, left={left}")
        print(f"  match_score for {seqA[i-1]}-{seqB[j-1]} = {match_score}")
        print(f"  diagonal + match = {diagonal + match_score}")
        print(f"  up + gap = {up + gap_penalty}")
        print(f"  left + gap = {left + gap_penalty}")
        
        if score == diagonal + match_score:
            print(f"  -> Choosing diagonal: {seqA[i-1]}-{seqB[j-1]}")
            aligned_a = seqA[i-1] + aligned_a
            aligned_b = seqB[j-1] + aligned_b
            i -= 1
            j -= 1
        elif score == up + gap_penalty:
            print(f"  -> Choosing up: _-{seqB[j-1]}")
            aligned_a = "_" + aligned_a
            aligned_b = seqB[j-1] + aligned_b
            j -= 1
        else:  # score == left + gap_penalty
            print(f"  -> Choosing left: {seqA[i-1]}-_")
            aligned_a = seqA[i-1] + aligned_a
            aligned_b = "_" + aligned_b
            i -= 1
    
    # Handle remaining
    print(f"After main loop: i={i}, j={j}")
    while i > 0:
        print(f"  Remaining A: {seqA[i-1]}")
        aligned_a = seqA[i-1] + aligned_a
        aligned_b = "_" + aligned_b
        i -= 1
    while j > 0:
        print(f"  Remaining B: {seqB[j-1]}")
        aligned_a = "_" + aligned_a
        aligned_b = seqB[j-1] + aligned_b
        j -= 1
    
    print(f"\nFinal alignment:")
    print(f"A: {aligned_a}")
    print(f"B: {aligned_b}")
    print(f"Score: {M[len(seqA)][len(seqB)]}")

if __name__ == "__main__":
    debug_traceback()
