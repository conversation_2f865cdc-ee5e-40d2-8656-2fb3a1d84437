#!/usr/bin/env python3
"""
测试不同K值的聚类效果
使用肘部法则和轮廓系数选择最佳K值
"""

import subprocess
import sys
import os

def run_kmeans(k, data_file, max_iterations=50):
    """运行K-means聚类"""
    cmd = f"python3 kmeans.py {k} {data_file} {max_iterations}"
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=".")
        if result.returncode == 0:
            return True
        else:
            print(f"K={k} 运行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"K={k} 运行出错: {e}")
        return False

def evaluate_clustering_simple(data_file, cluster_file):
    """简化的聚类评估"""
    try:
        cmd = f"python3 evaluate_clustering.py {data_file} {cluster_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            output = result.stdout
            # 提取关键指标
            wcss = None
            silhouette = None
            
            for line in output.split('\n'):
                if '簇内平方和 (WCSS):' in line:
                    wcss = float(line.split(':')[1].strip())
                elif '平均轮廓系数' in line and '基于' in line:
                    # 提取轮廓系数
                    parts = line.split(':')[1].strip()
                    silhouette = float(parts.split()[0])
            
            return wcss, silhouette
        else:
            print(f"评估失败: {result.stderr}")
            return None, None
    except Exception as e:
        print(f"评估出错: {e}")
        return None, None

def test_k_values(data_file, k_range, max_iterations=50):
    """测试不同的K值"""
    print("=== 测试不同K值的聚类效果 ===")
    print(f"数据文件: {data_file}")
    print(f"K值范围: {k_range}")
    print(f"最大迭代次数: {max_iterations}")
    print()
    
    results = []
    
    for k in k_range:
        print(f"正在测试 K={k}...")
        
        # 运行K-means
        success = run_kmeans(k, data_file, max_iterations)
        if not success:
            continue
        
        # 评估结果
        wcss, silhouette = evaluate_clustering_simple(data_file, "kmeans.out")
        
        if wcss is not None and silhouette is not None:
            results.append({
                'k': k,
                'wcss': wcss,
                'silhouette': silhouette
            })
            print(f"  K={k}: WCSS={wcss:.2f}, 轮廓系数={silhouette:.3f}")
        else:
            print(f"  K={k}: 评估失败")
        
        print()
    
    return results

def analyze_results(results):
    """分析结果并给出建议"""
    if not results:
        print("没有有效的结果可以分析")
        return
    
    print("=== 结果分析 ===")
    print("K值\tWCSS\t\t轮廓系数\t评价")
    print("-" * 50)
    
    best_silhouette_k = None
    best_silhouette_score = -1
    
    for result in results:
        k = result['k']
        wcss = result['wcss']
        silhouette = result['silhouette']
        
        # 评价聚类质量
        if silhouette > 0.5:
            quality = "优秀"
        elif silhouette > 0.3:
            quality = "良好"
        elif silhouette > 0.1:
            quality = "一般"
        else:
            quality = "较差"
        
        print(f"{k}\t{wcss:.2f}\t\t{silhouette:.3f}\t\t{quality}")
        
        # 记录最佳轮廓系数
        if silhouette > best_silhouette_score:
            best_silhouette_score = silhouette
            best_silhouette_k = k
    
    print()
    print("=== 建议 ===")
    
    # 肘部法则分析
    print("1. 肘部法则分析 (WCSS):")
    wcss_values = [r['wcss'] for r in results]
    wcss_decreases = []
    for i in range(1, len(wcss_values)):
        decrease = wcss_values[i-1] - wcss_values[i]
        wcss_decreases.append(decrease)
    
    if len(wcss_decreases) >= 2:
        # 找到WCSS下降幅度显著减小的点
        max_decrease_change = 0
        elbow_k = results[0]['k']
        
        for i in range(1, len(wcss_decreases)):
            change = wcss_decreases[i-1] - wcss_decreases[i]
            if change > max_decrease_change:
                max_decrease_change = change
                elbow_k = results[i]['k']
        
        print(f"   推荐K值: {elbow_k} (基于WCSS下降趋势)")
    
    # 轮廓系数分析
    print("2. 轮廓系数分析:")
    if best_silhouette_k:
        print(f"   最佳K值: {best_silhouette_k} (轮廓系数: {best_silhouette_score:.3f})")
    
    # 综合建议
    print("3. 综合建议:")
    if best_silhouette_score > 0.3:
        print(f"   建议使用 K={best_silhouette_k}，聚类质量较好")
    else:
        print("   所有K值的聚类质量都不理想，建议:")
        print("   - 考虑数据预处理 (标准化、特征选择)")
        print("   - 尝试其他聚类算法")
        print("   - 检查数据是否适合聚类分析")

def main():
    if len(sys.argv) < 2:
        print("使用方法: python test_different_k.py <数据文件> [最小K] [最大K] [最大迭代次数]")
        print("示例: python test_different_k.py data_proj_2/yeast.dat.txt 2 8 30")
        sys.exit(1)
    
    data_file = sys.argv[1]
    min_k = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    max_k = int(sys.argv[3]) if len(sys.argv) > 3 else 8
    max_iterations = int(sys.argv[4]) if len(sys.argv) > 4 else 30
    
    if not os.path.exists(data_file):
        print(f"错误: 数据文件 {data_file} 不存在")
        sys.exit(1)
    
    k_range = range(min_k, max_k + 1)
    
    # 测试不同K值
    results = test_k_values(data_file, k_range, max_iterations)
    
    # 分析结果
    analyze_results(results)

if __name__ == "__main__":
    main()
