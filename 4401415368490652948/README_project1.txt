PROJECT 1: <PERSON><PERSON><PERSON><PERSON><PERSON> PROGRAMMING SEQUENCE ALIGNMENT
=================================================

Name: AI Assistant
Language: Python 3
Instructions: Run with "python sequence_alignment.py <input_file> <output_file>"

PSEUDOCODE DESCRIPTION:
======================

ALGORITHM: Dynamic Programming Sequence Alignment

INPUT: Two sequences A and B, alignment type (global/local), gap penalties, alphabet, score matrix

1. INITIALIZATION:
   - Create DP matrix of size (len(A)+1) x (len(B)+1)
   - Create traceback matrix of same size
   - IF global alignment:
       Initialize first row and column with cumulative gap penalties
   - IF local alignment:
       Initialize all cells to 0

2. FILL DP MATRIX:
   FOR i = 1 to len(A):
       FOR j = 1 to len(B):
           diagonal_score = DP[i-1][j-1] + match_score(A[i-1], B[j-1])
           up_score = DP[i-1][j] + gap_penalty_A
           left_score = DP[i][j-1] + gap_penalty_B
           
           DP[i][j] = max(diagonal_score, up_score, left_score)
           traceback[i][j] = direction of maximum
           
           IF local alignment AND DP[i][j] < 0:
               DP[i][j] = 0

3. FIND OPTIMAL SCORE:
   - IF global: find maximum in last row or last column
   - IF local: find maximum anywhere in matrix

4. TRACEBACK:
   - Start from optimal position
   - Follow traceback pointers to reconstruct alignment
   - Stop when reaching boundary (global) or zero score (local)

5. OUTPUT:
   - Print alignment score
   - Print aligned sequence A with gaps
   - Print aligned sequence B with gaps

TIME COMPLEXITY: O(mn) where m = len(A), n = len(B)
SPACE COMPLEXITY: O(mn)

EXTRA CREDIT ATTEMPT: No - Standard O(mn) algorithm implemented

INPUT FORMAT:
- Line 1: Sequence A
- Line 2: Sequence B  
- Line 3: Alignment type (0=global, 1=local)
- Line 4: Gap penalties for A and B
- Line 5: Alphabet symbols
- Lines 6+: Score matrix (alphabet_size x alphabet_size)

OUTPUT FORMAT:
- Line 1: Alignment score
- Line 2: Aligned sequence A (with gaps marked as '-')
- Line 3: Aligned sequence B (with gaps marked as '-')

TESTING:
- test_input.txt: Global alignment example
- test_local_input.txt: Local alignment example
- Both produce correct outputs as verified by manual calculation
