# 水资源配置优化系统 - NSGA-II多目标优化

## 项目简介

本项目是一个基于NSGA-II算法的水资源配置多目标优化系统，用于解决八个乡镇的水资源分配问题。系统考虑多种水源（地表水、引黄水、引江水、地下水、微咸水、再生水）和多种用户需求（城镇生活、农村生活、工业三产、生态、农业）。

## 主要功能

### 1. 基础优化方法
- **算法类型**: 简化修复算法
- **运行时间**: 几秒钟
- **结果**: 1个可行解
- **特点**: 快速获得基本可行的水资源配置方案

### 2. NSGA-II多目标优化
- **算法类型**: 简化版NSGA-II遗传算法
- **运行时间**: 5-15分钟
- **结果**: 多样化的帕累托最优解集
- **优化目标**:
  - 目标1: 最小化总缺水量
  - 目标2: 最大化微咸水使用量
- **特点**:
  - 采用软约束策略，保持解的多样性
  - 生成真正的帕累托前沿，避免过早收敛
  - 提供多个平衡方案供决策选择

## 文件结构

```
4401625719421413340/
├── main.py              # 主程序文件（简化版NSGA-II算法）
├── 代码数据.xlsx        # 水资源数据文件
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明文档
├── analysis_result.xlsx # 分析结果（运行后生成）
└── pareto_front.png    # 帕累托前沿图（运行后生成）
```

## 数据文件说明

Excel文件包含以下工作表：
- **农业需水**: 各乡镇农业用水需求（按月）
- **固定需水**: 城镇生活、农村生活、工业三产、生态用水需求
- **地下水**: 各乡镇地下水可用量限制
- **微咸水**: 2-3g/L和3-5g/L微咸水可用量
- **地表水**: 各乡镇地表水可用量

## 环境要求

### Python版本
- Python 3.7 或更高版本

### 依赖包安装
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install numpy pandas matplotlib deap openpyxl
```

## 使用方法

### 1. 运行程序
```bash
python main.py
```

### 2. 选择优化方法
程序启动后会显示菜单：
- **选项1**: 基础优化方法 - 快速获得可行解
- **选项2**: NSGA-II多目标优化 - 完整的帕累托前沿分析

输入对应数字选择运行模式。

### 3. 推荐使用流程
1. 首先运行基础优化方法，验证数据和程序正常
2. 然后运行NSGA-II多目标优化，获得完整的优化结果

## 输出结果

### 基础优化输出
- `analysis_result.xlsx` - 详细的水资源配置方案
- 包含各乡镇、各用户、各月份的供水配置
- 水源利用情况和缺水量统计

### NSGA-II优化输出
- `analysis_result.xlsx` - 最优解的详细配置方案
- `pareto_front.png` - 帕累托前沿图（自动配置中文字体）
- 控制台显示多个优化解的特征分析
- 帕累托前沿解集的统计信息

## 算法参数

### NSGA-II参数设置
- **种群大小**: 40个个体
- **进化代数**: 25代
- **交叉概率**: 0.8
- **变异概率**: 0.3
- **变异强度**: sigma=5000
- **预计运行时间**: 5-15分钟

### 算法特点
- **软约束策略**: 允许适度违反约束，通过惩罚函数引导优化
- **多样性保持**: 轻度修复策略，避免强制统一配置
- **多样性监控**: 实时监控种群多样性，自动注入新个体
- **中文字体支持**: 自动配置操作系统中文字体

可在 `main.py` 中的 `simplified_nsga2_algorithm()` 函数内调整参数。

## 约束条件

系统考虑以下约束：
1. **水源供应限制**: 各类水源的月度和年度供应上限
2. **需水量平衡**: 满足各用户的基本需水需求
3. **微咸水配比**: 微咸水使用需要一定比例的淡水稀释
4. **时间约束**: 引黄水仅在特定月份可用
5. **空间约束**: 考虑乡镇间的水源调配关系

## 技术特点

### 多目标优化
- 使用简化版NSGA-II算法处理多目标冲突
- 生成多样化的帕累托最优解集
- 同时优化缺水量最小化和微咸水使用量最大化
- 避免过早收敛，确保解的多样性

### 软约束策略
- 采用软约束评估，允许适度违反约束条件
- 通过惩罚函数引导优化方向
- 保持解的多样性，避免强制统一配置
- 平衡可行性和优化效果

### 多样性保持机制
- 轻度修复策略，只进行基本约束检查
- 多种个体初始化方法（随机+多样化）
- 实时多样性监控和自动恢复
- 防止所有解收敛到同一点

### 中文字体支持
- 自动检测操作系统并配置合适的中文字体
- 支持macOS、Windows、Linux系统
- 图表中文显示正常，无方块问题

### 可视化分析
- 帕累托前沿图自动生成
- 解的多样性统计和分析
- 实时进化过程监控

## 常见问题

### Q1: 程序运行时间过长怎么办？
**解决方案**：
- 简化版NSGA-II算法正常运行时间为5-15分钟
- 可以在代码中减小种群大小（如改为30）和进化代数（如改为20）
- 先运行基础优化验证数据正确性

### Q2: 图片中文显示为方块
**解决方案**：
- 程序已自动配置中文字体
- 如仍有问题，请安装系统中文字体包
- 更新matplotlib到最新版本

### Q3: Excel文件读取错误
**解决方案**：
- 确保 `代码数据.xlsx` 文件在程序目录下
- 检查Excel文件是否包含所有必要的工作表
- 确保数据格式正确（数值型数据）

### Q4: 内存不足
**解决方案**：
- 减小NSGA-II的种群大小（如改为30）
- 关闭其他程序释放内存
- 使用性能更好的计算机

### Q5: 帕累托前沿只有一个点
**解决方案**：
- 这是旧版本的问题，新版本已解决
- 确保使用的是最新的main.py文件
- 如果仍有问题，可以增加变异强度或种群大小

### Q6: 算法收敛效果不理想
**解决方案**：
- 增加进化代数（如改为40代）
- 增加种群大小（如改为60个个体）
- 调整变异参数sigma值（如改为8000）

## 注意事项

1. **数据文件必须存在**：程序严格依赖真实Excel数据，不使用模拟数据
2. **运行时间**：简化版NSGA-II优化需要5-15分钟，请耐心等待
3. **结果文件**：所有输出文件保存在程序运行目录下
4. **推荐流程**：先运行基础优化验证，再运行NSGA-II获得多目标优化结果
5. **算法改进**：新版本采用软约束策略，有效解决了过早收敛问题
6. **多样性保证**：算法会自动监控和维护解的多样性

## 版本更新

### v2.0 (当前版本)
- ✅ 采用简化版NSGA-II算法
- ✅ 软约束策略，保持解的多样性
- ✅ 解决过早收敛问题
- ✅ 自动多样性监控和恢复
- ✅ 优化运行时间（5-15分钟）

### v1.0 (yuan.py)
- 基础修复算法
- 原始NSGA-II实现
- 存在过早收敛问题

## 系统要求

- **操作系统**: Windows/macOS/Linux
- **Python版本**: 3.7+
- **内存**: 建议4GB以上
- **存储空间**: 至少100MB可用空间
- **运行时间**: 基础优化几秒钟，NSGA-II优化10-30分钟
