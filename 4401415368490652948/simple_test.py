#!/usr/bin/env python3

def needleman_wunsch_simple(seqA, seqB, score_matrix, gap_penalty):
    """Simple implementation based on your provided algorithm"""
    # Initialize scoring matrix
    M = [[0] * (len(seqB) + 1) for _ in range(len(seqA) + 1)]
    
    # Fill first row and column
    for i in range(len(seqA) + 1):
        M[i][0] = gap_penalty * i
    for j in range(len(seqB) + 1):
        M[0][j] = gap_penalty * j
    
    # Fill matrix
    for i in range(1, len(seqA) + 1):
        for j in range(1, len(seqB) + 1):
            match_score = score_matrix.get((seqA[i-1], seqB[j-1]), 0)
            match = M[i-1][j-1] + match_score
            delete = M[i-1][j] + gap_penalty
            insert = M[i][j-1] + gap_penalty
            M[i][j] = max(match, delete, insert)
    
    # Print matrix for debugging
    print("DP Matrix:")
    print("    ", end="")
    for c in " " + seqB:
        print(f"{c:6}", end="")
    print()
    for i in range(len(seqA) + 1):
        if i == 0:
            print(f"  ", end="")
        else:
            print(f"{seqA[i-1]} ", end="")
        for j in range(len(seqB) + 1):
            print(f"{M[i][j]:6.1f}", end="")
        print()
    
    # Traceback
    alignedA, alignedB = "", ""
    i, j = len(seqA), len(seqB)
    while i > 0 and j > 0:
        score = M[i][j]
        diagonal = M[i-1][j-1]
        up = M[i][j-1]
        left = M[i-1][j]
        
        match_score = score_matrix.get((seqA[i-1], seqB[j-1]), 0)
        
        if score == diagonal + match_score:
            alignedA = seqA[i-1] + alignedA
            alignedB = seqB[j-1] + alignedB
            i -= 1
            j -= 1
        elif score == up + gap_penalty:
            alignedA = "_" + alignedA
            alignedB = seqB[j-1] + alignedB
            j -= 1
        else:  # score == left + gap_penalty
            alignedA = seqA[i-1] + alignedA
            alignedB = "_" + alignedB
            i -= 1
    
    # Handle remaining characters
    while i > 0:
        alignedA = seqA[i-1] + alignedA
        alignedB = "_" + alignedB
        i -= 1
    while j > 0:
        alignedA = "_" + alignedA
        alignedB = seqB[j-1] + alignedB
        j -= 1
    
    return alignedA, alignedB, M[len(seqA)][len(seqB)]

# Test with the example
seqA = "AATGC"
seqB = "AGGC"
score_matrix = {
    ('A', 'A'): 1, ('A', 'T'): 0, ('A', 'G'): 0, ('A', 'C'): 0,
    ('T', 'A'): 0, ('T', 'T'): 1, ('T', 'G'): 0, ('T', 'C'): 0,
    ('G', 'A'): 0, ('G', 'T'): 0, ('G', 'G'): 1, ('G', 'C'): 0,
    ('C', 'A'): 0, ('C', 'T'): 0, ('C', 'G'): 0, ('C', 'C'): 1,
}
gap_penalty = 0

alignedA, alignedB, score = needleman_wunsch_simple(seqA, seqB, score_matrix, gap_penalty)
print(f"\nResult:")
print(f"Score: {score}")
print(f"Seq A: {alignedA}")
print(f"Seq B: {alignedB}")
