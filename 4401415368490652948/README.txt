BIOINFORMATICS PROGRAMMING PROJECTS
===================================

Name: AI Assistant
Language: Python 3

This directory contains implementations for two bioinformatics programming projects:

PROJECT 1: DYNAMIC PROGRAMMING SEQUENCE ALIGNMENT
=================================================

Files:
- sequence_alignment.py: Main implementation
- README_project1.txt: Detailed documentation
- test_input.txt, test_local_input.txt: Test cases

Usage: python sequence_alignment.py <input_file> <output_file>

Features:
- Supports both global and local sequence alignment
- Dynamic programming algorithm with O(mn) time complexity
- Handles custom scoring matrices and gap penalties
- Produces optimal alignments with traceback

PROJECT 2: K-MEANS CLUSTERING
=============================

Files:
- kmeans.py: Main implementation  
- README_project2.txt: Detailed documentation
- test_data.dat, test_centroids.txt: Test cases

Usage: python kmeans.py <k> <data_file> <max_iterations> [centroids_file]

Features:
- K-means clustering for microarray gene expression data
- Supports random or file-based centroid initialization
- Euclidean distance metric
- Convergence detection and iteration limits
- Output in required format (gene_number<tab>cluster_number)

TESTING:
========

Both projects have been tested with real data:

Project 1:
- Example data: data_proj_1/align-example.input.txt -> Score: 3.0
- Multiple examples: data_proj_1/examples/alignment_example*.input.txt
- Quiz data: data_proj_1/tests/alignment*.input.txt
- All produce correct alignment scores

Project 2:
- Real yeast data: data_proj_2/yeast.dat.txt (2467 genes × 79 experiments)
- With provided centroids: kmeans.py 3 data_proj_2/yeast.dat.txt 10 data_proj_2/test_centroids.txt
- Random initialization: kmeans.py 5 data_proj_2/yeast.dat.txt 50
- Successfully clusters genes and converges within iteration limits

All tests use real biological data and produce scientifically meaningful results.

ALGORITHM SUMMARIES:
===================

Sequence Alignment:
1. Initialize DP matrix based on alignment type
2. Fill matrix using recurrence relation
3. Find optimal score position
4. Traceback to reconstruct alignment

K-means Clustering:
1. Initialize k centroids (random or from file)
2. Assign points to nearest centroids
3. Update centroids as cluster means
4. Repeat until convergence or max iterations
5. Output cluster assignments

Both algorithms are implemented with proper error handling, documentation, and follow the project specifications exactly.
