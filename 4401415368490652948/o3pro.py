# aligner.py (修正版)
import sys
from enum import Enum

# 使用枚举类型来清晰地表示回溯方向
class Pointer(Enum):
    """用于追踪比对路径的指针。"""
    DIAG = 0    # 来自左上方（对角线）
    UP = 1      # 来自上方
    LEFT = 2    # 来自左方

class Matrix(Enum):
    """代表Gotoh算法中的三个矩阵。"""
    M = 0   # 主矩阵 (匹配/错配)
    IX = 1  # X方向的空位矩阵 (seq_A vs gap)
    IY = 2  # Y方向的空位矩阵 (gap vs seq_B)

def parse_input(file_path):
    """
    根据项目规范解析输入文件。
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f if line.strip()]

    data = {}
    data['seq_a'] = lines[0]
    data['seq_b'] = lines[1]
    data['is_local'] = (int(lines[2]) == 1)

    try:
        # 尝试将空位罚分行解析为两个浮点数
        penalties = list(map(float, lines[3].split()))
        if len(penalties) >= 2:
            data['gap_open'] = penalties[0]
            data['gap_extend'] = penalties[1]
        else: # 如果只有一个数，则认为是线性罚分
             data['gap_open'] = 0.0
             data['gap_extend'] = penalties[0]
    except (ValueError, IndexError):
        # 对于示例中的 '0 0 0 0'，我们安全地解析为 0, 0
        print(f"警告: 无法从 '{lines[3]}' 标准解析空位罚分。将尝试特殊解析或使用默认值 (0, 0)。")
        if lines[3].replace(' ', '').replace('0','').replace('.','') == '':
            data['gap_open'] = 0.0
            data['gap_extend'] = 0.0
        else:
            raise ValueError("无法解析空位罚分行。")

    sub_matrix_lines = []
    for line in reversed(lines):
        parts = line.split()
        if len(parts) == 5 and parts[0].isdigit():
             sub_matrix_lines.insert(0, parts)
        else:
            if len(sub_matrix_lines) > 0: break
    
    data['alphabet'] = sorted(list(set(data['seq_a'] + data['seq_b'])))
    
    sub_matrix = {char: {} for char in data['alphabet']}
    for char1 in data['alphabet']:
        for char2 in data['alphabet']:
            sub_matrix[char1][char2] = 0

    for line_parts in sub_matrix_lines:
        char1, char2, score = line_parts[2], line_parts[3], float(line_parts[4])
        if char1 in sub_matrix and char2 in sub_matrix[char1]:
            sub_matrix[char1][char2] = score
        if char2 in sub_matrix and char1 in sub_matrix[char2]:
            sub_matrix[char2][char1] = score

    data['sub_matrix'] = sub_matrix
    return data

def run_alignment(seq_a, seq_b, is_local, gap_open, gap_extend, sub_matrix):
    """
    使用Gotoh算法进行序列比对，支持仿射空位罚分。
    此实现可以处理全局比对和局部比对。
    """
    len_a, len_b = len(seq_a), len(seq_b)
    neg_inf = float('-inf')

    # 初始化得分和指针矩阵
    M = [[0.0] * (len_b + 1) for _ in range(len_a + 1)]
    Ix = [[0.0] * (len_b + 1) for _ in range(len_a + 1)]
    Iy = [[0.0] * (len_b + 1) for _ in range(len_a + 1)]
    
    ptr_M = [[[] for _ in range(len_b + 1)] for _ in range(len_a + 1)]
    ptr_Ix = [[[] for _ in range(len_b + 1)] for _ in range(len_a + 1)]
    ptr_Iy = [[[] for _ in range(len_b + 1)] for _ in range(len_a + 1)]

    # --- 初始化 ---
    if not is_local: # 全局比对的初始化
        M[0][0] = 0
        Ix[0][0] = neg_inf
        Iy[0][0] = neg_inf
        # 初始化第一列
        for i in range(1, len_a + 1):
            M[i][0] = neg_inf
            Iy[i][0] = neg_inf
            # 修正点: 严谨处理第一列的Ix矩阵得分和指针
            # 对于第一列，只能从M[i-1][0]或Ix[i-1][0]过来
            open_cost = M[i-1][0] - (gap_open + gap_extend)
            extend_cost = Ix[i-1][0] - gap_extend
            Ix[i][0] = max(open_cost, extend_cost)
            if Ix[i][0] == open_cost:
                ptr_Ix[i][0].append((Pointer.UP, Matrix.M))
            if Ix[i][0] == extend_cost and extend_cost > neg_inf:
                ptr_Ix[i][0].append((Pointer.UP, Matrix.IX))

        # 初始化第一行
        for j in range(1, len_b + 1):
            M[0][j] = neg_inf
            Ix[0][j] = neg_inf
            # 修正点: 严谨处理第一行的Iy矩阵得分和指针
            # 对于第一行，只能从M[0][j-1]或Iy[0][j-1]过来
            open_cost = M[0][j-1] - (gap_open + gap_extend)
            extend_cost = Iy[0][j-1] - gap_extend
            Iy[0][j] = max(open_cost, extend_cost)
            if Iy[0][j] == open_cost:
                ptr_Iy[0][j].append((Pointer.LEFT, Matrix.M))
            if Iy[0][j] == extend_cost and extend_cost > neg_inf:
                ptr_Iy[0][j].append((Pointer.LEFT, Matrix.IY))

    # --- 填充矩阵 ---
    max_score = 0.0 if is_local else neg_inf
    max_pos = [(0, 0)]

    for i in range(1, len_a + 1):
        for j in range(1, len_b + 1):
            char_a = seq_a[i-1]
            char_b = seq_b[j-1]
            sub_score = sub_matrix.get(char_a, {}).get(char_b, 0)

            # 计算 M[i,j]
            scores_m = {
                Matrix.M: M[i-1][j-1] + sub_score,
                Matrix.IX: Ix[i-1][j-1] + sub_score,
                Matrix.IY: Iy[i-1][j-1] + sub_score
            }
            best_m = max(scores_m.values())
            M[i][j] = best_m
            ptr_M[i][j] = [(Pointer.DIAG, mat_type) for mat_type, score in scores_m.items() if score == best_m]
            
            # 计算 Ix[i,j]
            scores_ix = {
                Matrix.M: M[i-1][j] - (gap_open + gap_extend),
                Matrix.IX: Ix[i-1][j] - gap_extend
            }
            best_ix = max(scores_ix.values())
            Ix[i][j] = best_ix
            ptr_Ix[i][j] = [(Pointer.UP, mat_type) for mat_type, score in scores_ix.items() if score == best_ix]

            # 计算 Iy[i,j]
            scores_iy = {
                Matrix.M: M[i][j-1] - (gap_open + gap_extend),
                Matrix.IY: Iy[i][j-1] - gap_extend
            }
            best_iy = max(scores_iy.values())
            Iy[i][j] = best_iy
            ptr_Iy[i][j] = [(Pointer.LEFT, mat_type) for mat_type, score in scores_iy.items() if score == best_iy]

            if is_local:
                M[i][j] = max(0, M[i][j])
                Ix[i][j] = max(0, Ix[i][j])
                Iy[i][j] = max(0, Iy[i][j])
                current_max = max(M[i][j], Ix[i][j], Iy[i][j])
                if current_max > max_score:
                    max_score = current_max
                    max_pos = [(i, j)]
                elif current_max == max_score and current_max > 0:
                    max_pos.append((i, j))

    # --- 回溯 ---
    alignments = set()
    if is_local:
        if max_score == 0: return 0.0, [("", "")]
        for i_start, j_start in max_pos:
            current_max = max(M[i_start][j_start], Ix[i_start][j_start], Iy[i_start][j_start])
            if M[i_start][j_start] == current_max:
                traceback_recursive(i_start, j_start, Matrix.M, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M, Ix, Iy, "", "", alignments, is_local)
            if Ix[i_start][j_start] == current_max:
                traceback_recursive(i_start, j_start, Matrix.IX, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M, Ix, Iy, "", "", alignments, is_local)
            if Iy[i_start][j_start] == current_max:
                traceback_recursive(i_start, j_start, Matrix.IY, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M, Ix, Iy, "", "", alignments, is_local)
        best_score = max_score
    else:
        final_scores = {Matrix.M: M[len_a][len_b], Matrix.IX: Ix[len_a][len_b], Matrix.IY: Iy[len_a][len_b]}
        best_score = max(final_scores.values())
        for mat_type, score in final_scores.items():
            if score == best_score:
                traceback_recursive(len_a, len_b, mat_type, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M, Ix, Iy, "", "", alignments, is_local)
                
    return best_score, sorted(list(alignments))

def traceback_recursive(i, j, matrix_type, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M_scores, Ix_scores, Iy_scores, aln_a, aln_b, alignments, is_local):
    """
    递归地进行回溯，以找到所有最优比对路径。
    """
    # 递归的基线条件
    if not is_local and i == 0 and j == 0:
        alignments.add((aln_a, aln_b))
        return
    
    if is_local:
        current_score = 0
        if matrix_type == Matrix.M: current_score = M_scores[i][j]
        elif matrix_type == Matrix.IX: current_score = Ix_scores[i][j]
        else: current_score = Iy_scores[i][j]
        if current_score == 0:
            alignments.add((aln_a, aln_b))
            return

    # 根据当前矩阵类型选择正确的指针矩阵
    ptr_matrices = {Matrix.M: ptr_M, Matrix.IX: ptr_Ix, Matrix.IY: ptr_Iy}
    current_ptr_matrix = ptr_matrices[matrix_type]
    
    # 检查是否有可用的指针，如果没有则为递归终点
    if not current_ptr_matrix[i][j]:
        # 对于全局比对，如果不在(0,0)处中断，说明路径不完整，可忽略
        # 对于局部比对，这不应该发生，因为有得分=0的基线条件
        return

    # 遍历所有可能的路径
    for pointer, next_matrix_type in current_ptr_matrix[i][j]:
        if pointer == Pointer.DIAG:
            traceback_recursive(i-1, j-1, next_matrix_type, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M_scores, Ix_scores, Iy_scores, seq_a[i-1] + aln_a, seq_b[j-1] + aln_b, alignments, is_local)
        elif pointer == Pointer.UP:
            traceback_recursive(i-1, j, next_matrix_type, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M_scores, Ix_scores, Iy_scores, seq_a[i-1] + aln_a, '_' + aln_b, alignments, is_local)
        elif pointer == Pointer.LEFT:
            traceback_recursive(i, j-1, next_matrix_type, seq_a, seq_b, ptr_M, ptr_Ix, ptr_Iy, M_scores, Ix_scores, Iy_scores, '_' + aln_a, seq_b[j-1] + aln_b, alignments, is_local)

def format_and_write_output(output_path, score, alignments):
    """格式化并写入最终输出到文件。"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"{score:.1f}\n")
        
        sorted_alignments = sorted(list(set(alignments)))
        
        for i, (aln_a, aln_b) in enumerate(sorted_alignments):
            if i > 0:
                f.write("\n")
            f.write(f"{aln_a}\n")
            f.write(f"{aln_b}\n")
    print(f"输出已成功写入到 {output_path}")

def main():
    """主函数，运行比对程序。"""
    if len(sys.argv) != 2:
        print("用法: python aligner.py <输入文件>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = f"{input_file}.output"

    try:
        params = parse_input(input_file)
    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_file}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"解析输入文件时发生错误: {e}")
        sys.exit(1)
        
    best_score, alignments = run_alignment(
        params['seq_a'], params['seq_b'], params['is_local'], 
        params['gap_open'], params['gap_extend'], params['sub_matrix']
    )
    
    format_and_write_output(output_file, best_score, alignments)

if __name__ == "__main__":
    main()