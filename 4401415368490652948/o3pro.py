#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dynamic-Programming sequence alignment
  • global  (0) : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  • local   (1) : <PERSON><PERSON><PERSON>
  • affine gaps : Gotoh 1982
满足课程 Project-1 输入/输出格式（见示例文件）。

参考:
  Needleman & Wunsch 1970  :contentReference[oaicite:0]{index=0}
  Gotoh 1982               :contentReference[oaicite:1]{index=1}
  输入样例说明             
"""
from collections import defaultdict
import math
import sys

GAP_CHAR = '_'          # 与官方输出保持一致
NEG_INF  = -1e100       # “－∞” 表示非法/极小值


# ─────────────────────────  读入  ──────────────────────────
def parse_input(fp):
    """
    解析题目输入格式：
        seqA
        seqB
        local_flag          # 0=global / 1=local
        gopenA gextA gopenB gextB
        |ΣB|
        alphabetB
        |ΣA|
        alphabetA
        |ΣB|×|ΣA| 行  (row col symB symA score)
    """
    with open(fp, encoding='utf-8') as f:
        lines = [ln.strip() for ln in f if ln.strip() != '']

    seqA, seqB         = lines[0], lines[1]
    local_flag         = (lines[2] == '1')
    gopenA, gextA, gopenB, gextB = map(float, lines[3].split())

    sizeB = int(lines[4]); alphabetB = lines[5]
    sizeA = int(lines[6]); alphabetA = lines[7]

    score = defaultdict(lambda: 0.0)
    idx = 8
    for _ in range(sizeB * sizeA):
        _, _, sb, sa, val = lines[idx].split()
        score[(sb, sa)] = float(val)
        idx += 1

    return (seqA, seqB, local_flag,
            gopenA, gextA, gopenB, gextB, score)


# ────────────────────────  主对齐函数  ───────────────────────
def align(seqA, seqB, local,
          gopenA, gextA, gopenB, gextB,
          scorefunc):
    m, n = len(seqA), len(seqB)

    # 三张矩阵 (Gotoh 仿射罚分)
    M  = [[NEG_INF]*(n+1) for _ in range(m+1)]
    Ix = [[NEG_INF]*(n+1) for _ in range(m+1)]
    Iy = [[NEG_INF]*(n+1) for _ in range(m+1)]

    # 前驱指针 (记录等价路径)
    ptrM = [[[] for _ in range(n+1)] for _ in range(m+1)]
    ptrX = [[[] for _ in range(n+1)] for _ in range(m+1)]
    ptrY = [[[] for _ in range(n+1)] for _ in range(m+1)]

    # 初始化
    M[0][0] = 0.0
    if not local:            # 全局：首行首列按罚分累加
        for i in range(1, m+1):
            Iy[i][0] = -(gopenB + (i-1)*gextB)
            M[i][0]  = Iy[i][0]
            ptrY[i][0].append(('Iy', i-1, 0))
        for j in range(1, n+1):
            Ix[0][j] = -(gopenA + (j-1)*gextA)
            M[0][j]  = Ix[0][j]
            ptrX[0][j].append(('Ix', 0, j-1))

    best_score = 0.0 if local else NEG_INF
    best_cells = [('M', 0, 0)] if not local else []

    # 动态规划填表
    for i in range(1, m+1):
        for j in range(1, n+1):
            a, b = seqA[i-1], seqB[j-1]
            s    = scorefunc[(b, a)]

            # Ix: gap 在 B（即 A 有缺口）
            open_ = M[i][j-1] - gopenA
            ext_  = Ix[i][j-1] - gextA
            Ix[i][j] = max(open_, ext_)
            if abs(Ix[i][j] - open_) < 1e-9:
                ptrX[i][j].append(('M', i, j-1))
            if abs(Ix[i][j] - ext_) < 1e-9:
                ptrX[i][j].append(('Ix', i, j-1))

            # Iy: gap 在 A（即 B 有缺口）
            open_ = M[i-1][j] - gopenB
            ext_  = Iy[i-1][j] - gextB
            Iy[i][j] = max(open_, ext_)
            if abs(Iy[i][j] - open_) < 1e-9:
                ptrY[i][j].append(('M', i-1, j))
            if abs(Iy[i][j] - ext_) < 1e-9:
                ptrY[i][j].append(('Iy', i-1, j))

            # M: 末尾匹配/错配
            cand = [
                (M[i-1][j-1], 'M'),
                (Ix[i-1][j-1], 'Ix'),
                (Iy[i-1][j-1], 'Iy')
            ]
            best_pre = max(v for v, _ in cand)
            M[i][j]  = best_pre + s
            for v, tag in cand:
                if abs(best_pre - v) < 1e-9:
                    ptrM[i][j].append((tag, i-1, j-1))

            # 局部：不允许负分
            if local:
                if M[i][j]  < 0: M[i][j]  = 0.0; ptrM[i][j] = []
                if Ix[i][j] < 0: Ix[i][j] = 0.0; ptrX[i][j] = []
                if Iy[i][j] < 0: Iy[i][j] = 0.0; ptrY[i][j] = []

            # 记录当前最优
            for mat_id, val in (('M', M[i][j]), ('Ix', Ix[i][j]), ('Iy', Iy[i][j])):
                if val > best_score + 1e-9:
                    best_score = val
                    best_cells = [(mat_id, i, j)]
                elif abs(val - best_score) < 1e-9:
                    best_cells.append((mat_id, i, j))

    if not local:     # 全局：唯一最优终点
        best_cells = [('M', m, n)]
        best_score = M[m][n]

    # ─────────────  回溯提取所有最优对齐  ─────────────
    results = []
    stack   = [([], [], mat, i, j) for (mat, i, j) in best_cells]

    while stack:
        alA, alB, mat, i, j = stack.pop()

        # 判断局部比对的“严格停点”
        if local:
            score_here = {'M': M, 'Ix': Ix, 'Iy': Iy}[mat][i][j]
            # 只有落在 M 且得分 0 才算完成一条对齐
            if score_here == 0.0:
                if mat == 'M':
                    results.append((''.join(reversed(alA)), ''.join(reversed(alB))))
                continue
        else:  # 全局：回到矩阵左上角
            if i == 0 and j == 0:
                results.append((''.join(reversed(alA)), ''.join(reversed(alB))))
                continue

        # 向前追溯
        if mat == 'M':
            for prev, ii, jj in ptrM[i][j]:
                stack.append((alA+[seqA[i-1]], alB+[seqB[j-1]], prev, ii, jj))
        elif mat == 'Ix':
            for prev, ii, jj in ptrX[i][j]:
                stack.append((alA+[GAP_CHAR], alB+[seqB[j-1]], prev, ii, jj))
        elif mat == 'Iy':
            for prev, ii, jj in ptrY[i][j]:
                stack.append((alA+[seqA[i-1]], alB+[GAP_CHAR], prev, ii, jj))

    # 去重并排序
    uniq = sorted(set(results))
    return best_score, uniq


# ─────────────────────────  主程序  ─────────────────────────
def main():
    if len(sys.argv) != 3:
        print("用法: python align.py <inputfile> <outputfile>")
        sys.exit(1)

    inp, outp = sys.argv[1], sys.argv[2]
    (seqA, seqB, local,
     gopenA, gextA, gopenB, gextB, score) = parse_input(inp)

    best_score, alignments = align(seqA, seqB, local,
                                   gopenA, gextA, gopenB, gextB, score)

    with open(outp, 'w', encoding='utf-8') as f:
        f.write(f"{best_score:.1f}\n\n")
        for k, (a, b) in enumerate(alignments):
            f.write(a + "\n")
            f.write(b + "\n")
            if k != len(alignments) - 1:
                f.write("\n")


if __name__ == "__main__":
    main()
