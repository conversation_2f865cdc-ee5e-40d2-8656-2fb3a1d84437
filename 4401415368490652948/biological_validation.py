#!/usr/bin/env python3
"""
生物学验证聚类结果
分析聚类中基因的功能一致性
"""

import sys
from collections import defaultdict, Counter

def load_gene_annotations(filename):
    """加载基因功能注释"""
    annotations = {}
    with open(filename, 'r') as f:
        for i, line in enumerate(f):
            if line.strip():
                # 使用空格分隔，但要处理多个空格的情况
                parts = line.strip().split()
                if len(parts) >= 3:
                    gene_name = parts[0]
                    function_category = parts[1]
                    # 剩余部分作为详细功能描述
                    detailed_function = ' '.join(parts[2:])
                    annotations[i] = {
                        'name': gene_name,
                        'category': function_category,
                        'detail': detailed_function
                    }
    return annotations

def load_clusters(filename):
    """加载聚类结果"""
    clusters = {}
    with open(filename, 'r') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split('\t')
                gene_id = int(parts[0]) - 1  # 转换为0索引
                cluster_id = int(parts[1]) - 1  # 转换为0索引
                clusters[gene_id] = cluster_id
    return clusters

def analyze_functional_enrichment(annotations, clusters, k):
    """分析功能富集"""
    print("=== 功能富集分析 ===")
    
    # 按聚类分组基因
    cluster_genes = defaultdict(list)
    for gene_id, cluster_id in clusters.items():
        if gene_id in annotations:
            cluster_genes[cluster_id].append(gene_id)
    
    for cluster_id in range(k):
        genes_in_cluster = cluster_genes[cluster_id]
        if not genes_in_cluster:
            continue
            
        print(f"\n聚类 {cluster_id + 1} ({len(genes_in_cluster)} 个基因):")
        
        # 统计功能分类
        categories = []
        for gene_id in genes_in_cluster:
            if gene_id in annotations:
                category = annotations[gene_id]['category']
                categories.append(category)
        
        # 计算功能分类频率
        category_counts = Counter(categories)
        total_genes = len(categories)
        
        print("主要功能分类:")
        for category, count in category_counts.most_common(10):
            percentage = (count / total_genes) * 100
            print(f"  - {category}: {count} 个基因 ({percentage:.1f}%)")
        
        # 显示一些具体基因例子
        print("基因示例:")
        for i, gene_id in enumerate(genes_in_cluster[:5]):
            if gene_id in annotations:
                gene_info = annotations[gene_id]
                print(f"  - {gene_info['name']}: {gene_info['category']} - {gene_info['detail']}")

def calculate_functional_purity(annotations, clusters, k):
    """计算功能纯度"""
    print("\n=== 功能纯度分析 ===")
    
    cluster_purities = []
    
    for cluster_id in range(k):
        # 获取该聚类中的基因
        genes_in_cluster = [gene_id for gene_id, c_id in clusters.items() 
                           if c_id == cluster_id and gene_id in annotations]
        
        if not genes_in_cluster:
            continue
        
        # 统计功能分类
        categories = [annotations[gene_id]['category'] for gene_id in genes_in_cluster]
        category_counts = Counter(categories)
        
        # 计算纯度（最大类别占比）
        max_count = max(category_counts.values()) if category_counts else 0
        purity = max_count / len(genes_in_cluster) if genes_in_cluster else 0
        cluster_purities.append(purity)
        
        most_common_category = category_counts.most_common(1)[0] if category_counts else ("未知", 0)
        
        print(f"聚类 {cluster_id + 1}:")
        print(f"  - 基因数量: {len(genes_in_cluster)}")
        print(f"  - 主要功能: {most_common_category[0]} ({most_common_category[1]} 个基因)")
        print(f"  - 功能纯度: {purity:.3f}")
    
    avg_purity = sum(cluster_purities) / len(cluster_purities) if cluster_purities else 0
    print(f"\n平均功能纯度: {avg_purity:.3f}")
    
    return avg_purity

def analyze_expression_patterns(data_file, clusters, k):
    """分析表达模式"""
    print("\n=== 表达模式分析 ===")
    
    # 加载数据
    data = []
    with open(data_file, 'r') as f:
        for line in f:
            if line.strip():
                values = [float(x) for x in line.strip().split('\t')]
                data.append(values)
    
    # 按聚类计算平均表达模式
    for cluster_id in range(k):
        genes_in_cluster = [gene_id for gene_id, c_id in clusters.items() if c_id == cluster_id]
        
        if not genes_in_cluster:
            continue
        
        # 计算该聚类的平均表达值
        cluster_data = [data[gene_id] for gene_id in genes_in_cluster]
        avg_expression = [0.0] * len(cluster_data[0])
        
        for gene_expr in cluster_data:
            for i, val in enumerate(gene_expr):
                avg_expression[i] += val
        
        for i in range(len(avg_expression)):
            avg_expression[i] /= len(cluster_data)
        
        # 找出表达变化最大的实验条件
        max_expr = max(avg_expression)
        min_expr = min(avg_expression)
        max_idx = avg_expression.index(max_expr)
        min_idx = avg_expression.index(min_expr)
        
        print(f"聚类 {cluster_id + 1}:")
        print(f"  - 基因数量: {len(genes_in_cluster)}")
        print(f"  - 最高表达条件: 实验 {max_idx + 1} (表达值: {max_expr:.3f})")
        print(f"  - 最低表达条件: 实验 {min_idx + 1} (表达值: {min_expr:.3f})")
        print(f"  - 表达变化范围: {max_expr - min_expr:.3f}")

def suggest_improvements(silhouette_score, functional_purity):
    """建议改进方法"""
    print("\n=== 改进建议 ===")
    
    if silhouette_score < 0.3:
        print("轮廓系数较低，建议:")
        print("- 尝试不同的K值 (如 k=4, 5, 6)")
        print("- 使用不同的初始化方法")
        print("- 考虑数据预处理 (标准化、降维)")
        print("- 尝试其他聚类算法 (层次聚类、DBSCAN)")
    
    if functional_purity < 0.4:
        print("功能纯度较低，建议:")
        print("- 增加聚类数量以获得更细粒度的分组")
        print("- 考虑基于功能的特征选择")
        print("- 使用生物学先验知识指导聚类")
    
    print("\n验证聚类结果的其他方法:")
    print("1. 文献验证: 查找已知的基因功能研究")
    print("2. GO富集分析: 使用Gene Ontology数据库")
    print("3. 通路分析: 使用KEGG等通路数据库")
    print("4. 交叉验证: 使用不同参数重复聚类")
    print("5. 专家评估: 请生物学专家评估结果合理性")

def main():
    if len(sys.argv) != 4:
        print("使用方法: python biological_validation.py <数据文件> <聚类结果文件> <基因注释文件>")
        print("示例: python biological_validation.py data_proj_2/yeast.dat.txt kmeans.out data_proj_2/yeast_gene_names.txt")
        sys.exit(1)
    
    data_file = sys.argv[1]
    cluster_file = sys.argv[2]
    annotation_file = sys.argv[3]
    
    try:
        # 加载数据
        print("正在加载数据...")
        annotations = load_gene_annotations(annotation_file)
        clusters = load_clusters(cluster_file)
        k = max(clusters.values()) + 1
        
        print(f"数据概况:")
        print(f"- 基因注释数量: {len(annotations)}")
        print(f"- 聚类结果数量: {len(clusters)}")
        print(f"- 聚类数量: {k}")
        
        # 功能富集分析
        analyze_functional_enrichment(annotations, clusters, k)
        
        # 功能纯度分析
        functional_purity = calculate_functional_purity(annotations, clusters, k)
        
        # 表达模式分析
        analyze_expression_patterns(data_file, clusters, k)
        
        # 改进建议 (需要轮廓系数，这里使用之前的结果)
        suggest_improvements(0.110, functional_purity)  # 使用之前计算的轮廓系数
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
