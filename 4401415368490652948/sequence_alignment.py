#!/usr/bin/env python3
"""
Dynamic Programming Sequence Alignment
Implements both global and local sequence alignment algorithms
Based on Needlem<PERSON>-<PERSON> and Smith-Waterman algorithms
"""

import sys

class SequenceAligner:
    def __init__(self):
        self.seq_a = ""
        self.seq_b = ""
        self.is_local = False
        self.gap_open_a = 0
        self.gap_extend_a = 0
        self.gap_open_b = 0
        self.gap_extend_b = 0
        self.alphabet_a = ""
        self.alphabet_b = ""
        self.score_matrix = {}
        self.dp_matrix = []
        
    def parse_input_file(self, filename):
        """Parse the input file according to project specifications"""
        with open(filename, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]

        line_idx = 0

        # Parse sequence A
        self.seq_a = lines[line_idx]
        line_idx += 1

        # Parse sequence B
        self.seq_b = lines[line_idx]
        line_idx += 1

        # Parse alignment type (0 = global, 1 = local)
        self.is_local = int(lines[line_idx]) == 1
        line_idx += 1

        # Parse gap penalties (4 values: open_A, extend_A, open_B, extend_B)
        gap_penalties = lines[line_idx].split()
        self.gap_open_a = float(gap_penalties[0])
        self.gap_extend_a = float(gap_penalties[1])
        self.gap_open_b = float(gap_penalties[2])
        self.gap_extend_b = float(gap_penalties[3])
        line_idx += 1

        # Parse alphabet size for A
        alphabet_size_a = int(lines[line_idx])
        line_idx += 1

        # Parse alphabet for A
        self.alphabet_a = lines[line_idx]
        line_idx += 1

        # Parse alphabet size for B
        alphabet_size_b = int(lines[line_idx])
        line_idx += 1

        # Parse alphabet for B
        self.alphabet_b = lines[line_idx]
        line_idx += 1

        # Parse score matrix entries
        self.score_matrix = {}
        for i in range(alphabet_size_a * alphabet_size_b):
            parts = lines[line_idx + i].split()
            char_a = parts[2]
            char_b = parts[3]
            score = float(parts[4])
            self.score_matrix[(char_a, char_b)] = score
    
    def needleman_wunsch_align(self):
        """Standard Needleman-Wunsch algorithm for global alignment"""
        seq_a, seq_b = self.seq_a, self.seq_b
        gap_penalty = self.gap_open_a  # Use gap_open_a as the gap penalty

        # Initialize scoring matrix
        rows, cols = len(seq_a) + 1, len(seq_b) + 1
        self.dp_matrix = [[0.0 for _ in range(cols)] for _ in range(rows)]

        # Fill first row and column with gap penalties
        for i in range(rows):
            self.dp_matrix[i][0] = gap_penalty * i
        for j in range(cols):
            self.dp_matrix[0][j] = gap_penalty * j

        # Fill the matrix
        for i in range(1, rows):
            for j in range(1, cols):
                # Match/mismatch score
                match_score = self.score_matrix.get((seq_a[i-1], seq_b[j-1]), 0)
                match = self.dp_matrix[i-1][j-1] + match_score

                # Gap scores
                delete = self.dp_matrix[i-1][j] + gap_penalty  # Gap in seq B
                insert = self.dp_matrix[i][j-1] + gap_penalty  # Gap in seq A

                self.dp_matrix[i][j] = max(match, delete, insert)



        return self.dp_matrix[rows-1][cols-1]

    def smith_waterman_align(self):
        """Smith-Waterman algorithm for local alignment"""
        rows = len(self.seq_a) + 1
        cols = len(self.seq_b) + 1

        # Initialize scoring matrix (all zeros for local alignment)
        self.dp_matrix = [[0.0 for _ in range(cols)] for _ in range(rows)]

        max_score = 0.0
        max_i, max_j = 0, 0

        # Fill the matrix
        for i in range(1, rows):
            for j in range(1, cols):
                # Match/mismatch score
                match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)
                match = self.dp_matrix[i-1][j-1] + match_score

                # Gap scores
                delete = self.dp_matrix[i-1][j] - self.gap_open_a  # Gap in seq B
                insert = self.dp_matrix[i][j-1] - self.gap_open_b  # Gap in seq A

                # Local alignment: never go below 0
                self.dp_matrix[i][j] = max(0, match, delete, insert)

                # Track maximum score position
                if self.dp_matrix[i][j] > max_score:
                    max_score = self.dp_matrix[i][j]
                    max_i, max_j = i, j

        return max_score, max_i, max_j

    def traceback_all_optimal(self, start_i, start_j):
        """Find all optimal alignments using recursive traceback"""
        alignments = []

        def traceback_recursive(i, j, aligned_a, aligned_b):
            # Base cases
            if i == 0 and j == 0:
                alignments.append((''.join(reversed(aligned_a)), ''.join(reversed(aligned_b))))
                return

            if self.is_local and self.dp_matrix[i][j] == 0:
                alignments.append((''.join(reversed(aligned_a)), ''.join(reversed(aligned_b))))
                return

            current_score = self.dp_matrix[i][j]
            possible_moves = []

            # Check diagonal move (match/mismatch)
            if i > 0 and j > 0:
                match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)
                if abs(self.dp_matrix[i-1][j-1] + match_score - current_score) < 1e-10:
                    possible_moves.append(('diagonal', i-1, j-1, self.seq_a[i-1], self.seq_b[j-1]))

            # Check up move (gap in sequence B)
            if i > 0:
                if abs(self.dp_matrix[i-1][j] - self.gap_open_a - current_score) < 1e-10:
                    possible_moves.append(('up', i-1, j, self.seq_a[i-1], '_'))

            # Check left move (gap in sequence A)
            if j > 0:
                if abs(self.dp_matrix[i][j-1] - self.gap_open_b - current_score) < 1e-10:
                    possible_moves.append(('left', i, j-1, '_', self.seq_b[j-1]))

            # Handle boundary conditions for global alignment
            if not self.is_local:
                while i > 0:
                    aligned_a.append(self.seq_a[i-1])
                    aligned_b.append('_')
                    i -= 1
                while j > 0:
                    aligned_a.append('_')
                    aligned_b.append(self.seq_b[j-1])
                    j -= 1
                if i == 0 and j == 0:
                    alignments.append((''.join(reversed(aligned_a)), ''.join(reversed(aligned_b))))
                return

            # Recursively explore all valid moves
            for _, new_i, new_j, char_a, char_b in possible_moves:
                new_aligned_a = aligned_a + [char_a]
                new_aligned_b = aligned_b + [char_b]
                traceback_recursive(new_i, new_j, new_aligned_a, new_aligned_b)

        traceback_recursive(start_i, start_j, [], [])
        return alignments

    def simple_traceback(self, start_i, start_j):
        """Simple traceback based on standard Needleman-Wunsch algorithm"""
        aligned_a, aligned_b = "", ""
        i, j = start_i, start_j
        gap_penalty = self.gap_open_a

        while i > 0 and j > 0:
            if self.is_local and self.dp_matrix[i][j] == 0:
                break

            score = self.dp_matrix[i][j]
            diagonal = self.dp_matrix[i-1][j-1]
            up = self.dp_matrix[i][j-1]
            left = self.dp_matrix[i-1][j]

            match_score = self.score_matrix.get((self.seq_a[i-1], self.seq_b[j-1]), 0)

            if score == diagonal + match_score:
                aligned_a = self.seq_a[i-1] + aligned_a
                aligned_b = self.seq_b[j-1] + aligned_b
                i -= 1
                j -= 1
            elif score == up + gap_penalty:
                aligned_a = "_" + aligned_a
                aligned_b = self.seq_b[j-1] + aligned_b
                j -= 1
            else:  # score == left + gap_penalty
                aligned_a = self.seq_a[i-1] + aligned_a
                aligned_b = "_" + aligned_b
                i -= 1

        # Handle remaining characters
        while i > 0:
            aligned_a = self.seq_a[i-1] + aligned_a
            aligned_b = "_" + aligned_b
            i -= 1
        while j > 0:
            aligned_a = "_" + aligned_a
            aligned_b = self.seq_b[j-1] + aligned_b
            j -= 1

        return aligned_a, aligned_b

    def align(self):
        """Perform sequence alignment"""
        if self.is_local:
            # Local alignment using Smith-Waterman
            max_score, max_i, max_j = self.smith_waterman_align()
            aligned_a, aligned_b = self.simple_traceback(max_i, max_j)
            return max_score, [(aligned_a, aligned_b)]
        else:
            # Global alignment using Needleman-Wunsch
            score = self.needleman_wunsch_align()
            rows = len(self.seq_a) + 1
            cols = len(self.seq_b) + 1

            # For now, return one optimal alignment
            aligned_a, aligned_b = self.simple_traceback(rows-1, cols-1)

            # Try to find all optimal alignments
            try:
                all_alignments = self.traceback_all_optimal(rows-1, cols-1)
                if all_alignments:
                    return score, all_alignments
            except:
                pass

            return score, [(aligned_a, aligned_b)]
    
    def write_output(self, filename, score, all_alignments):
        """Write all alignment results to output file"""
        with open(filename, 'w') as f:
            # Format score to avoid floating point precision issues
            if abs(score - round(score)) < 1e-10:
                f.write(f"{round(score):.1f}\n")
            else:
                f.write(f"{score:.1f}\n")

            # Write all alignments
            for i, (aligned_a, aligned_b) in enumerate(all_alignments):
                f.write(f"\n")  # Empty line before alignment
                f.write(f"{aligned_a}\n")
                f.write(f"{aligned_b}\n")
                if i < len(all_alignments) - 1:
                    f.write(f"\n")  # Empty line between alignments

            f.write(f"\n")  # Final empty line

def main():
    if len(sys.argv) != 3:
        print("Usage: python sequence_alignment.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    aligner = SequenceAligner()
    aligner.parse_input_file(input_file)

    score, all_alignments = aligner.align()
    aligner.write_output(output_file, score, all_alignments)

    print(f"Alignment completed. Results written to {output_file}")
    if abs(score - round(score)) < 1e-10:
        print(f"Score: {round(score):.1f}")
    else:
        print(f"Score: {score:.1f}")

    print(f"Found {len(all_alignments)} optimal alignment(s):")
    for i, (aligned_a, aligned_b) in enumerate(all_alignments):
        print(f"Alignment {i+1}:")
        print(f"  Sequence A: {aligned_a}")
        print(f"  Sequence B: {aligned_b}")

if __name__ == "__main__":
    main()
